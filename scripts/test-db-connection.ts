import { prisma } from '../lib/prisma'

async function testConnection() {
  try {
    // Intentar conectar a la base de datos
    await prisma.$connect()
    console.log('✅ Conexión a la base de datos exitosa!')
    
    // Verificar que podemos hacer una consulta básica
    const result = await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ Consulta de prueba exitosa:', result)
    
  } catch (error) {
    console.error('❌ Error de conexión a la base de datos:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testConnection()
