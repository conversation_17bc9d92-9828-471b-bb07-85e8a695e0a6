{"version": 3, "sources": [], "sections": [{"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/dev/FTW/actas-vecindad/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/dev/FTW/actas-vecindad/src/app/api/test-db/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport { prisma } from '../../../../lib/prisma'\n\nexport async function GET() {\n  try {\n    // Verificar conexión a la base de datos\n    await prisma.$connect()\n\n    // Hacer una consulta de prueba\n    const result = await prisma.$queryRaw`SELECT 1 as test, datetime('now') as timestamp`\n\n    // Convertir BigInt a string para serialización JSON\n    const serializedResult = JSON.parse(JSON.stringify(result, (key, value) =>\n      typeof value === 'bigint' ? value.toString() : value\n    ))\n\n    return NextResponse.json({\n      success: true,\n      message: 'Conexión a la base de datos exitosa',\n      data: serializedResult,\n      timestamp: new Date().toISOString()\n    })\n  } catch (error) {\n    console.error('Error de conexión a la base de datos:', error)\n    return NextResponse.json(\n      {\n        success: false,\n        message: 'Error de conexión a la base de datos',\n        error: error instanceof Error ? error.message : 'Error desconocido'\n      },\n      { status: 500 }\n    )\n  } finally {\n    await prisma.$disconnect()\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,wCAAwC;QACxC,MAAM,+GAAA,CAAA,SAAM,CAAC,QAAQ;QAErB,+BAA+B;QAC/B,MAAM,SAAS,MAAM,+GAAA,CAAA,SAAM,CAAC,SAAS,CAAC,8CAA8C,CAAC;QAErF,oDAAoD;QACpD,MAAM,mBAAmB,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC,QAAQ,CAAC,KAAK,QAC/D,OAAO,UAAU,WAAW,MAAM,QAAQ,KAAK;QAGjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;YACN,WAAW,IAAI,OAAO,WAAW;QACnC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GACA;YAAE,QAAQ;QAAI;IAElB,SAAU;QACR,MAAM,+GAAA,CAAA,SAAM,CAAC,WAAW;IAC1B;AACF"}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}