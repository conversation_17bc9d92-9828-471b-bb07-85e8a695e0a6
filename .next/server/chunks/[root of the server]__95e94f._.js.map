{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/dev/FTW/actas-vecindad/src/controllers/UserController.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { CreateUserUseCase } from '../use-cases/CreateUserUseCase'\nimport { CreateUserRequest } from '../types/entities/User'\n\nexport class UserController {\n  constructor(private createUserUseCase: CreateUserUseCase) {}\n\n  async create(request: NextRequest): Promise<NextResponse> {\n    try {\n      // Parsear el body de la request\n      const body = await this.parseRequestBody(request)\n      \n      // Validar estructura básica\n      this.validateRequestBody(body)\n      \n      // Extraer datos del request\n      const createUserRequest: CreateUserRequest = {\n        email: body.email,\n        password: body.password,\n        role: body.role\n      }\n      \n      // Ejecutar use case\n      const result = await this.createUserUseCase.execute(createUserRequest)\n      \n      // Retornar respuesta exitosa\n      return NextResponse.json(result, { status: 201 })\n      \n    } catch (error) {\n      return this.handleError(error)\n    }\n  }\n\n  private async parseRequestBody(request: NextRequest): Promise<any> {\n    try {\n      return await request.json()\n    } catch (error) {\n      throw new Error('Invalid JSON')\n    }\n  }\n\n  private validateRequestBody(body: any): void {\n    if (!body || typeof body !== 'object') {\n      throw new Error('Request body is required')\n    }\n    \n    if (!body.email) {\n      throw new Error('Email is required')\n    }\n    \n    if (!body.password) {\n      throw new Error('Password is required')\n    }\n    \n    if (!body.role) {\n      throw new Error('Role is required')\n    }\n  }\n\n  private handleError(error: unknown): NextResponse {\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error'\n    \n    // Errores de validación y lógica de negocio\n    if (this.isClientError(errorMessage)) {\n      return NextResponse.json(\n        { error: errorMessage },\n        { status: 400 }\n      )\n    }\n    \n    // Errores del servidor\n    console.error('Unexpected error in UserController:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n\n  private isClientError(errorMessage: string): boolean {\n    const clientErrorPatterns = [\n      'required',\n      'Invalid',\n      'already exists',\n      'not found',\n      'must be',\n      'format'\n    ]\n    \n    return clientErrorPatterns.some(pattern => \n      errorMessage.toLowerCase().includes(pattern.toLowerCase())\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAIO,MAAM;;IACX,YAAY,AAAQ,iBAAoC,CAAE;aAAtC,oBAAA;IAAuC;IAE3D,MAAM,OAAO,OAAoB,EAAyB;QACxD,IAAI;YACF,gCAAgC;YAChC,MAAM,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAEzC,4BAA4B;YAC5B,IAAI,CAAC,mBAAmB,CAAC;YAEzB,4BAA4B;YAC5B,MAAM,oBAAuC;gBAC3C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,MAAM,KAAK,IAAI;YACjB;YAEA,oBAAoB;YACpB,MAAM,SAAS,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAEpD,6BAA6B;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,QAAQ;gBAAE,QAAQ;YAAI;QAEjD,EAAE,OAAO,OAAO;YACd,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B;IACF;IAEA,MAAc,iBAAiB,OAAoB,EAAgB;QACjE,IAAI;YACF,OAAO,MAAM,QAAQ,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEQ,oBAAoB,IAAS,EAAQ;QAC3C,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;YACrC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,KAAK,EAAE;YACf,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,MAAM,IAAI,MAAM;QAClB;IACF;IAEQ,YAAY,KAAc,EAAgB;QAChD,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE9D,4CAA4C;QAC5C,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAa,GACtB;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;IAEQ,cAAc,YAAoB,EAAW;QACnD,MAAM,sBAAsB;YAC1B;YACA;YACA;YACA;YACA;YACA;SACD;QAED,OAAO,oBAAoB,IAAI,CAAC,CAAA,UAC9B,aAAa,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;IAE3D;AACF"}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/dev/FTW/actas-vecindad/src/use-cases/CreateUserUseCase.ts"], "sourcesContent": ["import { UserService } from '../services/UserService'\nimport { CreateUserRequest, CreateUserResponse, UserResponse } from '../types/entities/User'\n\nexport class CreateUserUseCase {\n  constructor(private userService: UserService) {}\n\n  async execute(request: CreateUserRequest): Promise<CreateUserResponse> {\n    // Validar campos requeridos\n    this.validateRequest(request)\n    \n    // Normalizar datos\n    const normalizedRequest = this.normalizeRequest(request)\n    \n    // Crear usuario usando el servicio\n    const user = await this.userService.createUser(normalizedRequest)\n    \n    // Retornar respuesta sin datos sensibles\n    return {\n      user: this.toUserResponse(user)\n    }\n  }\n\n  private validateRequest(request: CreateUserRequest): void {\n    if (!request.email || request.email.trim() === '') {\n      throw new Error('Email is required')\n    }\n    \n    if (!request.password || request.password.trim() === '') {\n      throw new Error('Password is required')\n    }\n    \n    if (!request.role || request.role.trim() === '') {\n      throw new Error('Role is required')\n    }\n  }\n\n  private normalizeRequest(request: CreateUserRequest): CreateUserRequest {\n    return {\n      email: request.email.trim().toLowerCase(),\n      password: request.password,\n      role: request.role\n    }\n  }\n\n  private toUserResponse(user: any): UserResponse {\n    return {\n      id: user.id,\n      email: user.email,\n      role: user.role,\n      created_at: user.created_at,\n      updated_at: user.updated_at\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAGO,MAAM;;IACX,YAAY,AAAQ,WAAwB,CAAE;aAA1B,cAAA;IAA2B;IAE/C,MAAM,QAAQ,OAA0B,EAA+B;QACrE,4BAA4B;QAC5B,IAAI,CAAC,eAAe,CAAC;QAErB,mBAAmB;QACnB,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC;QAEhD,mCAAmC;QACnC,MAAM,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;QAE/C,yCAAyC;QACzC,OAAO;YACL,MAAM,IAAI,CAAC,cAAc,CAAC;QAC5B;IACF;IAEQ,gBAAgB,OAA0B,EAAQ;QACxD,IAAI,CAAC,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,OAAO,IAAI;YACjD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,IAAI,OAAO,IAAI;YACvD,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAI,OAAO,IAAI;YAC/C,MAAM,IAAI,MAAM;QAClB;IACF;IAEQ,iBAAiB,OAA0B,EAAqB;QACtE,OAAO;YACL,OAAO,QAAQ,KAAK,CAAC,IAAI,GAAG,WAAW;YACvC,UAAU,QAAQ,QAAQ;YAC1B,MAAM,QAAQ,IAAI;QACpB;IACF;IAEQ,eAAe,IAAS,EAAgB;QAC9C,OAAO;YACL,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,YAAY,KAAK,UAAU;YAC3B,YAAY,KAAK,UAAU;QAC7B;IACF;AACF"}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/dev/FTW/actas-vecindad/src/services/UserService.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs'\nimport { IUserRepository } from '../repositories/interfaces/IUserRepository'\nimport { User, CreateUserData, UserFilters } from '../types/entities/User'\n\nexport class UserService {\n  constructor(private userRepository: IUserRepository) {}\n\n  async createUser(data: CreateUserData): Promise<User> {\n    // Validaciones de negocio\n    this.validateEmail(data.email)\n    this.validatePasswordStrength(data.password)\n    this.validateRole(data.role)\n\n    // Verificar que el email no exista\n    await this.ensureEmailIsUnique(data.email)\n\n    // Hash de la contraseña\n    const hashedPassword = await bcrypt.hash(data.password, 10)\n\n    // Crear usuario\n    return this.userRepository.create({\n      email: data.email,\n      hashed_password: hashedPassword,\n      role: data.role\n    })\n  }\n\n  async getUserById(id: number): Promise<User> {\n    const user = await this.userRepository.findById(id)\n    if (!user) {\n      throw new Error('User not found')\n    }\n    return user\n  }\n\n  async getUserByEmail(email: string): Promise<User | null> {\n    return this.userRepository.findByEmail(email)\n  }\n\n  async getAllUsers(filters?: UserFilters): Promise<User[]> {\n    return this.userRepository.findAll(filters)\n  }\n\n  async validatePassword(plainPassword: string, user: User): Promise<boolean> {\n    return bcrypt.compare(plainPassword, user.hashed_password)\n  }\n\n  async updateUser(id: number, data: Partial<CreateUserData>): Promise<User> {\n    // Verificar que el usuario existe\n    await this.getUserById(id)\n\n    const updateData: any = {}\n\n    if (data.email) {\n      this.validateEmail(data.email)\n      // Verificar que el nuevo email no esté en uso por otro usuario\n      const existingUser = await this.userRepository.findByEmail(data.email)\n      if (existingUser && existingUser.id !== id) {\n        throw new Error('Email already exists')\n      }\n      updateData.email = data.email\n    }\n\n    if (data.password) {\n      this.validatePasswordStrength(data.password)\n      updateData.hashed_password = await bcrypt.hash(data.password, 10)\n    }\n\n    if (data.role) {\n      this.validateRole(data.role)\n      updateData.role = data.role\n    }\n\n    return this.userRepository.update(id, updateData)\n  }\n\n  async deleteUser(id: number): Promise<void> {\n    // Verificar que el usuario existe\n    await this.getUserById(id)\n\n    return this.userRepository.delete(id)\n  }\n\n  // Métodos privados de validación\n  private validateEmail(email: string): void {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    if (!emailRegex.test(email)) {\n      throw new Error('Invalid email format')\n    }\n  }\n\n  private validatePasswordStrength(password: string): void {\n    if (password.length < 6) {\n      throw new Error('Password must be at least 6 characters long')\n    }\n  }\n\n  private validateRole(role: string): void {\n    const validRoles = ['ADMIN', 'ENGINEER', 'SOCIAL_ASSISTANT']\n    if (!validRoles.includes(role)) {\n      throw new Error('Invalid role')\n    }\n  }\n\n  private async ensureEmailIsUnique(email: string): Promise<void> {\n    const exists = await this.userRepository.existsByEmail(email)\n    if (exists) {\n      throw new Error('Email already exists')\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAIO,MAAM;;IACX,YAAY,AAAQ,cAA+B,CAAE;aAAjC,iBAAA;IAAkC;IAEtD,MAAM,WAAW,IAAoB,EAAiB;QACpD,0BAA0B;QAC1B,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK;QAC7B,IAAI,CAAC,wBAAwB,CAAC,KAAK,QAAQ;QAC3C,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI;QAE3B,mCAAmC;QACnC,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,KAAK;QAEzC,wBAAwB;QACxB,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;QAExD,gBAAgB;QAChB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;YAChC,OAAO,KAAK,KAAK;YACjB,iBAAiB;YACjB,MAAM,KAAK,IAAI;QACjB;IACF;IAEA,MAAM,YAAY,EAAU,EAAiB;QAC3C,MAAM,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;QAChD,IAAI,CAAC,MAAM;YACT,MAAM,IAAI,MAAM;QAClB;QACA,OAAO;IACT;IAEA,MAAM,eAAe,KAAa,EAAwB;QACxD,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;IACzC;IAEA,MAAM,YAAY,OAAqB,EAAmB;QACxD,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;IACrC;IAEA,MAAM,iBAAiB,aAAqB,EAAE,IAAU,EAAoB;QAC1E,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,eAAe,KAAK,eAAe;IAC3D;IAEA,MAAM,WAAW,EAAU,EAAE,IAA6B,EAAiB;QACzE,kCAAkC;QAClC,MAAM,IAAI,CAAC,WAAW,CAAC;QAEvB,MAAM,aAAkB,CAAC;QAEzB,IAAI,KAAK,KAAK,EAAE;YACd,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK;YAC7B,+DAA+D;YAC/D,MAAM,eAAe,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,KAAK;YACrE,IAAI,gBAAgB,aAAa,EAAE,KAAK,IAAI;gBAC1C,MAAM,IAAI,MAAM;YAClB;YACA,WAAW,KAAK,GAAG,KAAK,KAAK;QAC/B;QAEA,IAAI,KAAK,QAAQ,EAAE;YACjB,IAAI,CAAC,wBAAwB,CAAC,KAAK,QAAQ;YAC3C,WAAW,eAAe,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;QAChE;QAEA,IAAI,KAAK,IAAI,EAAE;YACb,IAAI,CAAC,YAAY,CAAC,KAAK,IAAI;YAC3B,WAAW,IAAI,GAAG,KAAK,IAAI;QAC7B;QAEA,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI;IACxC;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,kCAAkC;QAClC,MAAM,IAAI,CAAC,WAAW,CAAC;QAEvB,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;IACpC;IAEA,iCAAiC;IACzB,cAAc,KAAa,EAAQ;QACzC,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,MAAM,IAAI,MAAM;QAClB;IACF;IAEQ,yBAAyB,QAAgB,EAAQ;QACvD,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,MAAM,IAAI,MAAM;QAClB;IACF;IAEQ,aAAa,IAAY,EAAQ;QACvC,MAAM,aAAa;YAAC;YAAS;YAAY;SAAmB;QAC5D,IAAI,CAAC,WAAW,QAAQ,CAAC,OAAO;YAC9B,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAc,oBAAoB,KAAa,EAAiB;QAC9D,MAAM,SAAS,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;QACvD,IAAI,QAAQ;YACV,MAAM,IAAI,MAAM;QAClB;IACF;AACF"}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/dev/FTW/actas-vecindad/src/repositories/implementations/PrismaUserRepository.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\nimport { IUserRepository } from '../interfaces/IUserRepository'\nimport { User, CreateUserRepositoryData, UpdateUserData, UserFilters } from '../../types/entities/User'\n\nexport class PrismaUserRepository implements IUserRepository {\n  constructor(private prisma: PrismaClient) {}\n\n  async create(data: CreateUserRepositoryData): Promise<User> {\n    return this.prisma.user.create({\n      data: {\n        email: data.email,\n        hashed_password: data.hashed_password,\n        role: data.role\n      }\n    })\n  }\n\n  async findById(id: number): Promise<User | null> {\n    return this.prisma.user.findUnique({\n      where: { id }\n    })\n  }\n\n  async findByEmail(email: string): Promise<User | null> {\n    return this.prisma.user.findUnique({\n      where: { email }\n    })\n  }\n\n  async findAll(filters?: UserFilters): Promise<User[]> {\n    const where: any = {}\n    \n    if (filters?.email) {\n      where.email = filters.email\n    }\n    \n    if (filters?.role) {\n      where.role = filters.role\n    }\n\n    return this.prisma.user.findMany({\n      where\n    })\n  }\n\n  async update(id: number, data: UpdateUserData): Promise<User> {\n    return this.prisma.user.update({\n      where: { id },\n      data\n    })\n  }\n\n  async delete(id: number): Promise<void> {\n    await this.prisma.user.delete({\n      where: { id }\n    })\n  }\n\n  async existsByEmail(email: string): Promise<boolean> {\n    const user = await this.prisma.user.findUnique({\n      where: { email },\n      select: { id: true }\n    })\n    return user !== null\n  }\n\n  async findByRole(role: string): Promise<User[]> {\n    return this.prisma.user.findMany({\n      where: { role: role as any }\n    })\n  }\n\n  async count(filters?: UserFilters): Promise<number> {\n    const where: any = {}\n    \n    if (filters?.email) {\n      where.email = filters.email\n    }\n    \n    if (filters?.role) {\n      where.role = filters.role\n    }\n\n    return this.prisma.user.count({\n      where\n    })\n  }\n}\n"], "names": [], "mappings": ";;;AAIO,MAAM;;IACX,YAAY,AAAQ,MAAoB,CAAE;aAAtB,SAAA;IAAuB;IAE3C,MAAM,OAAO,IAA8B,EAAiB;QAC1D,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC7B,MAAM;gBACJ,OAAO,KAAK,KAAK;gBACjB,iBAAiB,KAAK,eAAe;gBACrC,MAAM,KAAK,IAAI;YACjB;QACF;IACF;IAEA,MAAM,SAAS,EAAU,EAAwB;QAC/C,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACjC,OAAO;gBAAE;YAAG;QACd;IACF;IAEA,MAAM,YAAY,KAAa,EAAwB;QACrD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACjC,OAAO;gBAAE;YAAM;QACjB;IACF;IAEA,MAAM,QAAQ,OAAqB,EAAmB;QACpD,MAAM,QAAa,CAAC;QAEpB,IAAI,SAAS,OAAO;YAClB,MAAM,KAAK,GAAG,QAAQ,KAAK;QAC7B;QAEA,IAAI,SAAS,MAAM;YACjB,MAAM,IAAI,GAAG,QAAQ,IAAI;QAC3B;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC/B;QACF;IACF;IAEA,MAAM,OAAO,EAAU,EAAE,IAAoB,EAAiB;QAC5D,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC7B,OAAO;gBAAE;YAAG;YACZ;QACF;IACF;IAEA,MAAM,OAAO,EAAU,EAAiB;QACtC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC5B,OAAO;gBAAE;YAAG;QACd;IACF;IAEA,MAAM,cAAc,KAAa,EAAoB;QACnD,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,OAAO;gBAAE;YAAM;YACf,QAAQ;gBAAE,IAAI;YAAK;QACrB;QACA,OAAO,SAAS;IAClB;IAEA,MAAM,WAAW,IAAY,EAAmB;QAC9C,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC/B,OAAO;gBAAE,MAAM;YAAY;QAC7B;IACF;IAEA,MAAM,MAAM,OAAqB,EAAmB;QAClD,MAAM,QAAa,CAAC;QAEpB,IAAI,SAAS,OAAO;YAClB,MAAM,KAAK,GAAG,QAAQ,KAAK;QAC7B;QAEA,IAAI,SAAS,MAAM;YACjB,MAAM,IAAI,GAAG,QAAQ,IAAI;QAC3B;QAEA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC5B;QACF;IACF;AACF"}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/dev/FTW/actas-vecindad/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG"}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/dev/FTW/actas-vecindad/src/factories/UserFactory.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\nimport { UserController } from '../controllers/UserController'\nimport { CreateUserUseCase } from '../use-cases/CreateUserUseCase'\nimport { UserService } from '../services/UserService'\nimport { PrismaUserRepository } from '../repositories/implementations/PrismaUserRepository'\nimport { prisma } from '../../lib/prisma'\n\nexport class UserFactory {\n  /**\n   * Crea una instancia completa del UserController con todas sus dependencias\n   */\n  static createUserController(): UserController {\n    // Crear repository\n    const userRepository = new PrismaUserRepository(prisma)\n    \n    // Crear service\n    const userService = new UserService(userRepository)\n    \n    // Crear use case\n    const createUserUseCase = new CreateUserUseCase(userService)\n    \n    // Crear controller\n    return new UserController(createUserUseCase)\n  }\n\n  /**\n   * Crea una instancia del UserService con sus dependencias\n   */\n  static createUserService(): UserService {\n    const userRepository = new PrismaUserRepository(prisma)\n    return new UserService(userRepository)\n  }\n\n  /**\n   * Crea una instancia del CreateUserUseCase con sus dependencias\n   */\n  static createCreateUserUseCase(): CreateUserUseCase {\n    const userService = this.createUserService()\n    return new CreateUserUseCase(userService)\n  }\n\n  /**\n   * Crea una instancia del UserRepository\n   */\n  static createUserRepository(): PrismaUserRepository {\n    return new PrismaUserRepository(prisma)\n  }\n\n  /**\n   * Factory para testing - permite inyectar mocks\n   */\n  static createUserControllerForTesting(\n    createUserUseCase: CreateUserUseCase\n  ): UserController {\n    return new UserController(createUserUseCase)\n  }\n\n  /**\n   * Factory para testing - permite inyectar repository mock\n   */\n  static createUserServiceForTesting(\n    userRepository: any\n  ): UserService {\n    return new UserService(userRepository)\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM;IACX;;GAEC,GACD,OAAO,uBAAuC;QAC5C,mBAAmB;QACnB,MAAM,iBAAiB,IAAI,gKAAA,CAAA,uBAAoB,CAAC,+GAAA,CAAA,SAAM;QAEtD,gBAAgB;QAChB,MAAM,cAAc,IAAI,gIAAA,CAAA,cAAW,CAAC;QAEpC,iBAAiB;QACjB,MAAM,oBAAoB,IAAI,0IAAA,CAAA,oBAAiB,CAAC;QAEhD,mBAAmB;QACnB,OAAO,IAAI,sIAAA,CAAA,iBAAc,CAAC;IAC5B;IAEA;;GAEC,GACD,OAAO,oBAAiC;QACtC,MAAM,iBAAiB,IAAI,gKAAA,CAAA,uBAAoB,CAAC,+GAAA,CAAA,SAAM;QACtD,OAAO,IAAI,gIAAA,CAAA,cAAW,CAAC;IACzB;IAEA;;GAEC,GACD,OAAO,0BAA6C;QAClD,MAAM,cAAc,IAAI,CAAC,iBAAiB;QAC1C,OAAO,IAAI,0IAAA,CAAA,oBAAiB,CAAC;IAC/B;IAEA;;GAEC,GACD,OAAO,uBAA6C;QAClD,OAAO,IAAI,gKAAA,CAAA,uBAAoB,CAAC,+GAAA,CAAA,SAAM;IACxC;IAEA;;GAEC,GACD,OAAO,+BACL,iBAAoC,EACpB;QAChB,OAAO,IAAI,sIAAA,CAAA,iBAAc,CAAC;IAC5B;IAEA;;GAEC,GACD,OAAO,4BACL,cAAmB,EACN;QACb,OAAO,IAAI,gIAAA,CAAA,cAAW,CAAC;IACzB;AACF"}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/dev/FTW/actas-vecindad/src/app/api/users/route.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { UserFactory } from '../../../factories/UserFactory'\n\nexport async function POST(request: NextRequest) {\n  const userController = UserFactory.createUserController()\n  return userController.create(request)\n}\n"], "names": [], "mappings": ";;;AACA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,MAAM,iBAAiB,iIAAA,CAAA,cAAW,CAAC,oBAAoB;IACvD,OAAO,eAAe,MAAM,CAAC;AAC/B"}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}