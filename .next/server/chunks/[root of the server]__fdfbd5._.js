module.exports = {

"[externals]/next/dist/compiled/next-server/app-route.runtime.dev.js [external] (next/dist/compiled/next-server/app-route.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-route.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/controllers/UserController.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "UserController": (()=>UserController)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
class UserController {
    createUserUseCase;
    constructor(createUserUseCase){
        this.createUserUseCase = createUserUseCase;
    }
    async create(request) {
        try {
            // Parsear el body de la request
            const body = await this.parseRequestBody(request);
            // Validar estructura básica
            this.validateRequestBody(body);
            // Extraer datos del request
            const createUserRequest = {
                email: body.email,
                password: body.password,
                role: body.role
            };
            // Ejecutar use case
            const result = await this.createUserUseCase.execute(createUserRequest);
            // Retornar respuesta exitosa
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result, {
                status: 201
            });
        } catch (error) {
            return this.handleError(error);
        }
    }
    async parseRequestBody(request) {
        try {
            return await request.json();
        } catch (error) {
            throw new Error('Invalid JSON');
        }
    }
    validateRequestBody(body) {
        if (!body || typeof body !== 'object') {
            throw new Error('Request body is required');
        }
        if (!body.email) {
            throw new Error('Email is required');
        }
        if (!body.password) {
            throw new Error('Password is required');
        }
        if (!body.role) {
            throw new Error('Role is required');
        }
    }
    handleError(error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        // Errores de validación y lógica de negocio
        if (this.isClientError(errorMessage)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: errorMessage
            }, {
                status: 400
            });
        }
        // Errores del servidor
        console.error('Unexpected error in UserController:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
    isClientError(errorMessage) {
        const clientErrorPatterns = [
            'required',
            'Invalid',
            'already exists',
            'not found',
            'must be',
            'format'
        ];
        return clientErrorPatterns.some((pattern)=>errorMessage.toLowerCase().includes(pattern.toLowerCase()));
    }
}
}}),
"[project]/src/use-cases/CreateUserUseCase.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "CreateUserUseCase": (()=>CreateUserUseCase)
});
class CreateUserUseCase {
    userService;
    constructor(userService){
        this.userService = userService;
    }
    async execute(request) {
        // Validar campos requeridos
        this.validateRequest(request);
        // Normalizar datos
        const normalizedRequest = this.normalizeRequest(request);
        // Crear usuario usando el servicio
        const user = await this.userService.createUser(normalizedRequest);
        // Retornar respuesta sin datos sensibles
        return {
            user: this.toUserResponse(user)
        };
    }
    validateRequest(request) {
        if (!request.email || request.email.trim() === '') {
            throw new Error('Email is required');
        }
        if (!request.password || request.password.trim() === '') {
            throw new Error('Password is required');
        }
        if (!request.role || request.role.trim() === '') {
            throw new Error('Role is required');
        }
    }
    normalizeRequest(request) {
        return {
            email: request.email.trim().toLowerCase(),
            password: request.password,
            role: request.role
        };
    }
    toUserResponse(user) {
        return {
            id: user.id,
            email: user.email,
            role: user.role,
            created_at: user.created_at,
            updated_at: user.updated_at
        };
    }
}
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[project]/src/services/UserService.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "UserService": (()=>UserService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
;
class UserService {
    userRepository;
    constructor(userRepository){
        this.userRepository = userRepository;
    }
    async createUser(data) {
        // Validaciones de negocio
        this.validateEmail(data.email);
        this.validatePasswordStrength(data.password);
        this.validateRole(data.role);
        // Verificar que el email no exista
        await this.ensureEmailIsUnique(data.email);
        // Hash de la contraseña
        const hashedPassword = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(data.password, 10);
        // Crear usuario
        return this.userRepository.create({
            email: data.email,
            hashed_password: hashedPassword,
            role: data.role
        });
    }
    async getUserById(id) {
        const user = await this.userRepository.findById(id);
        if (!user) {
            throw new Error('User not found');
        }
        return user;
    }
    async getUserByEmail(email) {
        return this.userRepository.findByEmail(email);
    }
    async getAllUsers(filters) {
        return this.userRepository.findAll(filters);
    }
    async validatePassword(plainPassword, user) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(plainPassword, user.hashed_password);
    }
    async updateUser(id, data) {
        // Verificar que el usuario existe
        await this.getUserById(id);
        const updateData = {};
        if (data.email) {
            this.validateEmail(data.email);
            // Verificar que el nuevo email no esté en uso por otro usuario
            const existingUser = await this.userRepository.findByEmail(data.email);
            if (existingUser && existingUser.id !== id) {
                throw new Error('Email already exists');
            }
            updateData.email = data.email;
        }
        if (data.password) {
            this.validatePasswordStrength(data.password);
            updateData.hashed_password = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].hash(data.password, 10);
        }
        if (data.role) {
            this.validateRole(data.role);
            updateData.role = data.role;
        }
        return this.userRepository.update(id, updateData);
    }
    async deleteUser(id) {
        // Verificar que el usuario existe
        await this.getUserById(id);
        return this.userRepository.delete(id);
    }
    // Métodos privados de validación
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new Error('Invalid email format');
        }
    }
    validatePasswordStrength(password) {
        if (password.length < 6) {
            throw new Error('Password must be at least 6 characters long');
        }
    }
    validateRole(role) {
        const validRoles = [
            'ADMIN',
            'ENGINEER',
            'SOCIAL_ASSISTANT'
        ];
        if (!validRoles.includes(role)) {
            throw new Error('Invalid role');
        }
    }
    async ensureEmailIsUnique(email) {
        const exists = await this.userRepository.existsByEmail(email);
        if (exists) {
            throw new Error('Email already exists');
        }
    }
}
}}),
"[project]/src/repositories/user/UserRepository.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "UserRepository": (()=>UserRepository)
});
class UserRepository {
    prisma;
    constructor(prisma){
        this.prisma = prisma;
    }
    async create(data) {
        return this.prisma.user.create({
            data: {
                email: data.email,
                hashed_password: data.hashed_password,
                role: data.role
            }
        });
    }
    async findById(id) {
        return this.prisma.user.findUnique({
            where: {
                id
            }
        });
    }
    async findByEmail(email) {
        return this.prisma.user.findUnique({
            where: {
                email
            }
        });
    }
    async findAll(filters) {
        const where = {};
        if (filters?.email) {
            where.email = filters.email;
        }
        if (filters?.role) {
            where.role = filters.role;
        }
        return this.prisma.user.findMany({
            where
        });
    }
    async update(id, data) {
        return this.prisma.user.update({
            where: {
                id
            },
            data
        });
    }
    async delete(id) {
        await this.prisma.user.delete({
            where: {
                id
            }
        });
    }
    async existsByEmail(email) {
        const user = await this.prisma.user.findUnique({
            where: {
                email
            },
            select: {
                id: true
            }
        });
        return user !== null;
    }
    async findByRole(role) {
        return this.prisma.user.findMany({
            where: {
                role: role
            }
        });
    }
    async count(filters) {
        const where = {};
        if (filters?.email) {
            where.email = filters.email;
        }
        if (filters?.role) {
            where.role = filters.role;
        }
        return this.prisma.user.count({
            where
        });
    }
}
}}),
"[project]/src/repositories/user/mock.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "MockUserRepository": (()=>MockUserRepository)
});
class MockUserRepository {
    users = [];
    nextId = 1;
    async create(data) {
        const user = {
            id: this.nextId++,
            email: data.email,
            hashed_password: data.hashed_password,
            role: data.role,
            created_at: new Date(),
            updated_at: new Date()
        };
        this.users.push(user);
        return user;
    }
    async findById(id) {
        return this.users.find((user)=>user.id === id) || null;
    }
    async findByEmail(email) {
        return this.users.find((user)=>user.email === email) || null;
    }
    async findAll(filters) {
        let result = [
            ...this.users
        ];
        if (filters?.email) {
            result = result.filter((user)=>user.email === filters.email);
        }
        if (filters?.role) {
            result = result.filter((user)=>user.role === filters.role);
        }
        return result;
    }
    async update(id, data) {
        const userIndex = this.users.findIndex((user)=>user.id === id);
        if (userIndex === -1) {
            throw new Error('User not found');
        }
        const updatedUser = {
            ...this.users[userIndex],
            ...data,
            updated_at: new Date()
        };
        this.users[userIndex] = updatedUser;
        return updatedUser;
    }
    async delete(id) {
        const userIndex = this.users.findIndex((user)=>user.id === id);
        if (userIndex === -1) {
            throw new Error('User not found');
        }
        this.users.splice(userIndex, 1);
    }
    async existsByEmail(email) {
        return this.users.some((user)=>user.email === email);
    }
    async findByRole(role) {
        return this.users.filter((user)=>user.role === role);
    }
    async count(filters) {
        const filtered = await this.findAll(filters);
        return filtered.length;
    }
    // Métodos de utilidad para testing
    clear() {
        this.users = [];
        this.nextId = 1;
    }
    seed(users) {
        this.users = [
            ...users
        ];
        this.nextId = Math.max(...users.map((u)=>u.id), 0) + 1;
    }
}
}}),
"[project]/src/repositories/user/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// Main implementation
__turbopack_esm__({});
;
;
}}),
"[project]/src/repositories/user/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_esm__({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$repositories$2f$user$2f$UserRepository$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/repositories/user/UserRepository.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$repositories$2f$user$2f$mock$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/repositories/user/mock.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$repositories$2f$user$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_import__("[project]/src/repositories/user/index.ts [app-route] (ecmascript) <locals>");
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_import__("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: [
        'query'
    ]
});
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/factories/UserFactory.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "UserFactory": (()=>UserFactory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$controllers$2f$UserController$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/controllers/UserController.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$use$2d$cases$2f$CreateUserUseCase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/use-cases/CreateUserUseCase.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$UserService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/services/UserService.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$repositories$2f$user$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/repositories/user/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$repositories$2f$user$2f$UserRepository$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/repositories/user/UserRepository.ts [app-route] (ecmascript)");
;
;
;
;
;
class UserFactory {
    /**
   * Crea una instancia completa del UserController con todas sus dependencias
   */ static createUserController() {
        // Crear repository
        const userRepository = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$repositories$2f$user$2f$UserRepository$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserRepository"](__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"]);
        // Crear service
        const userService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$UserService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserService"](userRepository);
        // Crear use case
        const createUserUseCase = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$use$2d$cases$2f$CreateUserUseCase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CreateUserUseCase"](userService);
        // Crear controller
        return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$controllers$2f$UserController$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserController"](createUserUseCase);
    }
    /**
   * Crea una instancia del UserService con sus dependencias
   */ static createUserService() {
        const userRepository = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$repositories$2f$user$2f$UserRepository$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserRepository"](__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"]);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$UserService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserService"](userRepository);
    }
    /**
   * Crea una instancia del CreateUserUseCase con sus dependencias
   */ static createCreateUserUseCase() {
        const userService = this.createUserService();
        return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$use$2d$cases$2f$CreateUserUseCase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CreateUserUseCase"](userService);
    }
    /**
   * Crea una instancia del UserRepository
   */ static createUserRepository() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$repositories$2f$user$2f$UserRepository$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserRepository"](__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"]);
    }
    /**
   * Factory para testing - permite inyectar mocks
   */ static createUserControllerForTesting(createUserUseCase) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$controllers$2f$UserController$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserController"](createUserUseCase);
    }
    /**
   * Factory para testing - permite inyectar repository mock
   */ static createUserServiceForTesting(userRepository) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$UserService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserService"](userRepository);
    }
}
}}),
"[project]/src/app/api/users/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$factories$2f$UserFactory$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/factories/UserFactory.ts [app-route] (ecmascript)");
;
async function POST(request) {
    const userController = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$factories$2f$UserFactory$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["UserFactory"].createUserController();
    return userController.create(request);
}
}}),
"[project]/ (server-utils)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__fdfbd5._.js.map