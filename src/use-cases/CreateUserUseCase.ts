import { UserService } from '../services/UserService'
import { CreateUserRequest, CreateUserResponse, UserResponse } from '../types/entities/User'

export class CreateUserUseCase {
  constructor(private userService: UserService) {}

  async execute(request: CreateUserRequest): Promise<CreateUserResponse> {
    // Validar campos requeridos
    this.validateRequest(request)
    
    // Normalizar datos
    const normalizedRequest = this.normalizeRequest(request)
    
    // Crear usuario usando el servicio
    const user = await this.userService.createUser(normalizedRequest)
    
    // Retornar respuesta sin datos sensibles
    return {
      user: this.toUserResponse(user)
    }
  }

  private validateRequest(request: CreateUserRequest): void {
    if (!request.email || request.email.trim() === '') {
      throw new Error('Email is required')
    }
    
    if (!request.password || request.password.trim() === '') {
      throw new Error('Password is required')
    }
    
    if (!request.role || request.role.trim() === '') {
      throw new Error('Role is required')
    }
  }

  private normalizeRequest(request: CreateUserRequest): CreateUserRequest {
    return {
      email: request.email.trim().toLowerCase(),
      password: request.password,
      role: request.role
    }
  }

  private toUserResponse(user: any): UserResponse {
    return {
      id: user.id,
      email: user.email,
      role: user.role,
      created_at: user.created_at,
      updated_at: user.updated_at
    }
  }
}
