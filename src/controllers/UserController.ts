import { NextRequest, NextResponse } from 'next/server'
import { CreateUserUseCase } from '../use-cases/CreateUserUseCase'
import { CreateUserRequest } from '../types/entities/User'

export class UserController {
  constructor(private createUserUseCase: CreateUserUseCase) {}

  async create(request: NextRequest): Promise<NextResponse> {
    try {
      // Parsear el body de la request
      const body = await this.parseRequestBody(request)
      
      // Validar estructura básica
      this.validateRequestBody(body)
      
      // Extraer datos del request
      const createUserRequest: CreateUserRequest = {
        email: body.email,
        password: body.password,
        role: body.role
      }
      
      // Ejecutar use case
      const result = await this.createUserUseCase.execute(createUserRequest)
      
      // Retornar respuesta exitosa
      return NextResponse.json(result, { status: 201 })
      
    } catch (error) {
      return this.handleError(error)
    }
  }

  private async parseRequestBody(request: NextRequest): Promise<any> {
    try {
      return await request.json()
    } catch (error) {
      throw new Error('Invalid JSON')
    }
  }

  private validateRequestBody(body: any): void {
    if (!body || typeof body !== 'object') {
      throw new Error('Request body is required')
    }
    
    if (!body.email) {
      throw new Error('Email is required')
    }
    
    if (!body.password) {
      throw new Error('Password is required')
    }
    
    if (!body.role) {
      throw new Error('Role is required')
    }
  }

  private handleError(error: unknown): NextResponse {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    
    // Errores de validación y lógica de negocio
    if (this.isClientError(errorMessage)) {
      return NextResponse.json(
        { error: errorMessage },
        { status: 400 }
      )
    }
    
    // Errores del servidor
    console.error('Unexpected error in UserController:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }

  private isClientError(errorMessage: string): boolean {
    const clientErrorPatterns = [
      'required',
      'Invalid',
      'already exists',
      'not found',
      'must be',
      'format'
    ]
    
    return clientErrorPatterns.some(pattern => 
      errorMessage.toLowerCase().includes(pattern.toLowerCase())
    )
  }
}
