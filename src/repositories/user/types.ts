export interface UserRepositoryInterface {
  create(data: CreateUserRepositoryData): Promise<User>
  findById(id: number): Promise<User | null>
  findByEmail(email: string): Promise<User | null>
  findAll(filters?: UserFilters): Promise<User[]>
  update(id: number, data: UpdateUserRepositoryData): Promise<User>
  delete(id: number): Promise<void>
  existsByEmail(email: string): Promise<boolean>
  findByRole(role: string): Promise<User[]>
  count(filters?: UserFilters): Promise<number>
}

export interface CreateUserRepositoryData {
  email: string
  hashed_password: string
  role: string
}

export interface UpdateUserRepositoryData {
  email?: string
  hashed_password?: string
  role?: string
}

export interface User {
  id: number
  email: string
  hashed_password: string
  role: string
  created_at: Date
  updated_at: Date
}

export interface UserFilters {
  role?: string
  email?: string
}
