import { PrismaClient } from '@prisma/client'
import { UserRepositoryInterface, User, CreateUserRepositoryData, UpdateUserRepositoryData, UserFilters } from './types'

export class UserRepository implements UserRepositoryInterface {
  constructor(private prisma: PrismaClient) {}

  async create(data: CreateUserRepositoryData): Promise<User> {
    return this.prisma.user.create({
      data: {
        email: data.email,
        hashed_password: data.hashed_password,
        role: data.role
      }
    })
  }

  async findById(id: number): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id }
    })
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email }
    })
  }

  async findAll(filters?: UserFilters): Promise<User[]> {
    const where: any = {}
    
    if (filters?.email) {
      where.email = filters.email
    }
    
    if (filters?.role) {
      where.role = filters.role
    }

    return this.prisma.user.findMany({
      where
    })
  }

  async update(id: number, data: UpdateUserRepositoryData): Promise<User> {
    return this.prisma.user.update({
      where: { id },
      data
    })
  }

  async delete(id: number): Promise<void> {
    await this.prisma.user.delete({
      where: { id }
    })
  }

  async existsByEmail(email: string): Promise<boolean> {
    const user = await this.prisma.user.findUnique({
      where: { email },
      select: { id: true }
    })
    return user !== null
  }

  async findByRole(role: string): Promise<User[]> {
    return this.prisma.user.findMany({
      where: { role: role as any }
    })
  }

  async count(filters?: UserFilters): Promise<number> {
    const where: any = {}
    
    if (filters?.email) {
      where.email = filters.email
    }
    
    if (filters?.role) {
      where.role = filters.role
    }

    return this.prisma.user.count({
      where
    })
  }
}
