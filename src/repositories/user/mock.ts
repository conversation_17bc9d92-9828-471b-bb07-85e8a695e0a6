import { UserRepositoryInterface, User, CreateUserRepositoryData, UpdateUserRepositoryData, UserFilters } from './types'

export class MockUserRepository implements UserRepositoryInterface {
  private users: User[] = []
  private nextId = 1

  async create(data: CreateUserRepositoryData): Promise<User> {
    const user: User = {
      id: this.nextId++,
      email: data.email,
      hashed_password: data.hashed_password,
      role: data.role,
      created_at: new Date(),
      updated_at: new Date()
    }
    this.users.push(user)
    return user
  }

  async findById(id: number): Promise<User | null> {
    return this.users.find(user => user.id === id) || null
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.users.find(user => user.email === email) || null
  }

  async findAll(filters?: UserFilters): Promise<User[]> {
    let result = [...this.users]
    
    if (filters?.email) {
      result = result.filter(user => user.email === filters.email)
    }
    
    if (filters?.role) {
      result = result.filter(user => user.role === filters.role)
    }
    
    return result
  }

  async update(id: number, data: UpdateUserRepositoryData): Promise<User> {
    const userIndex = this.users.findIndex(user => user.id === id)
    if (userIndex === -1) {
      throw new Error('User not found')
    }
    
    const updatedUser = {
      ...this.users[userIndex],
      ...data,
      updated_at: new Date()
    }
    
    this.users[userIndex] = updatedUser
    return updatedUser
  }

  async delete(id: number): Promise<void> {
    const userIndex = this.users.findIndex(user => user.id === id)
    if (userIndex === -1) {
      throw new Error('User not found')
    }
    this.users.splice(userIndex, 1)
  }

  async existsByEmail(email: string): Promise<boolean> {
    return this.users.some(user => user.email === email)
  }

  async findByRole(role: string): Promise<User[]> {
    return this.users.filter(user => user.role === role)
  }

  async count(filters?: UserFilters): Promise<number> {
    const filtered = await this.findAll(filters)
    return filtered.length
  }

  // Métodos de utilidad para testing
  clear(): void {
    this.users = []
    this.nextId = 1
  }

  seed(users: User[]): void {
    this.users = [...users]
    this.nextId = Math.max(...users.map(u => u.id), 0) + 1
  }
}
