import { PrismaClient } from '@prisma/client'
import { UserController } from '../controllers/UserController'
import { CreateUserUseCase } from '../use-cases/CreateUserUseCase'
import { UserService } from '../services/UserService'
import { UserRepository } from '../repositories/user'
import { prisma } from '../../lib/prisma'

export class UserFactory {
  /**
   * Crea una instancia completa del UserController con todas sus dependencias
   */
  static createUserController(): UserController {
    // Crear repository
    const userRepository = new UserRepository(prisma)

    // Crear service
    const userService = new UserService(userRepository)

    // Crear use case
    const createUserUseCase = new CreateUserUseCase(userService)

    // Crear controller
    return new UserController(createUserUseCase)
  }

  /**
   * Crea una instancia del UserService con sus dependencias
   */
  static createUserService(): UserService {
    const userRepository = new UserRepository(prisma)
    return new UserService(userRepository)
  }

  /**
   * Crea una instancia del CreateUserUseCase con sus dependencias
   */
  static createCreateUserUseCase(): CreateUserUseCase {
    const userService = this.createUserService()
    return new CreateUserUseCase(userService)
  }

  /**
   * Crea una instancia del UserRepository
   */
  static createUserRepository(): UserRepository {
    return new UserRepository(prisma)
  }

  /**
   * Factory para testing - permite inyectar mocks
   */
  static createUserControllerForTesting(
    createUserUseCase: CreateUserUseCase
  ): UserController {
    return new UserController(createUserUseCase)
  }

  /**
   * Factory para testing - permite inyectar repository mock
   */
  static createUserServiceForTesting(
    userRepository: any
  ): UserService {
    return new UserService(userRepository)
  }
}
