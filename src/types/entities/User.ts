// Tipos para el dominio de Usuario
export type UserRole = 'ADMIN' | 'ENGINEER' | 'SOCIAL_ASSISTANT'

export interface User {
  id: number
  email: string
  hashed_password: string
  role: UserR<PERSON>
  created_at: Date
  updated_at: Date
}

export interface CreateUserData {
  email: string
  password: string
  role: UserRole
}

export interface CreateUserRepositoryData {
  email: string
  hashed_password: string
  role: UserR<PERSON>
}

export interface UpdateUserData {
  email?: string
  hashed_password?: string
  role?: UserRole
}

export interface UserFilters {
  email?: string
  role?: UserRole
}

// DTOs para API
export interface CreateUserRequest {
  email: string
  password: string
  role: UserRole
}

export interface CreateUserResponse {
  user: Omit<User, 'hashed_password'>
}

export interface UserResponse {
  id: number
  email: string
  role: UserRole
  created_at: Date
  updated_at: Date
}
