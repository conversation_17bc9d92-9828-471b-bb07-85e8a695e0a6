import bcrypt from 'bcryptjs'
import { UserRepositoryInterface, User, UserFilters } from '../repositories/user'
import { CreateUserData } from '../types/entities/User'

export class UserService {
  constructor(private userRepository: UserRepositoryInterface) {}

  async createUser(data: CreateUserData): Promise<User> {
    // Validaciones de negocio
    this.validateEmail(data.email)
    this.validatePasswordStrength(data.password)
    this.validateRole(data.role)

    // Verificar que el email no exista
    await this.ensureEmailIsUnique(data.email)

    // Hash de la contraseña
    const hashedPassword = await bcrypt.hash(data.password, 10)

    // Crear usuario
    return this.userRepository.create({
      email: data.email,
      hashed_password: hashedPassword,
      role: data.role
    })
  }

  async getUserById(id: number): Promise<User> {
    const user = await this.userRepository.findById(id)
    if (!user) {
      throw new Error('User not found')
    }
    return user
  }

  async getUserByEmail(email: string): Promise<User | null> {
    return this.userRepository.findByEmail(email)
  }

  async getAllUsers(filters?: UserFilters): Promise<User[]> {
    return this.userRepository.findAll(filters)
  }

  async validatePassword(plainPassword: string, user: User): Promise<boolean> {
    return bcrypt.compare(plainPassword, user.hashed_password)
  }

  async updateUser(id: number, data: Partial<CreateUserData>): Promise<User> {
    // Verificar que el usuario existe
    await this.getUserById(id)

    const updateData: any = {}

    if (data.email) {
      this.validateEmail(data.email)
      // Verificar que el nuevo email no esté en uso por otro usuario
      const existingUser = await this.userRepository.findByEmail(data.email)
      if (existingUser && existingUser.id !== id) {
        throw new Error('Email already exists')
      }
      updateData.email = data.email
    }

    if (data.password) {
      this.validatePasswordStrength(data.password)
      updateData.hashed_password = await bcrypt.hash(data.password, 10)
    }

    if (data.role) {
      this.validateRole(data.role)
      updateData.role = data.role
    }

    return this.userRepository.update(id, updateData)
  }

  async deleteUser(id: number): Promise<void> {
    // Verificar que el usuario existe
    await this.getUserById(id)

    return this.userRepository.delete(id)
  }

  // Métodos privados de validación
  private validateEmail(email: string): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format')
    }
  }

  private validatePasswordStrength(password: string): void {
    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters long')
    }
  }

  private validateRole(role: string): void {
    const validRoles = ['ADMIN', 'ENGINEER', 'SOCIAL_ASSISTANT']
    if (!validRoles.includes(role)) {
      throw new Error('Invalid role')
    }
  }

  private async ensureEmailIsUnique(email: string): Promise<void> {
    const exists = await this.userRepository.existsByEmail(email)
    if (exists) {
      throw new Error('Email already exists')
    }
  }
}
