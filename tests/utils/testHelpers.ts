import { prisma } from '../../lib/prisma'
import bcrypt from 'bcryptjs'

// Tipos para testing
export interface CreateUserData {
  email: string
  password: string
  role: 'ADMIN' | 'ENGINEER' | 'SOCIAL_ASSISTANT'
}

// Factory para crear usuarios de prueba
export const userFactory = {
  // Crear datos de usuario válidos
  build: (overrides: Partial<CreateUserData> = {}): CreateUserData => ({
    email: `test${Date.now()}@example.com`,
    password: 'password123',
    role: 'ENGINEER',
    ...overrides
  }),

  // Crear usuario en la base de datos
  create: async (overrides: Partial<CreateUserData> = {}) => {
    const userData = userFactory.build(overrides)
    const hashedPassword = await bcrypt.hash(userData.password, 10)
    
    return await prisma.user.create({
      data: {
        email: userData.email,
        hashed_password: hashedPassword,
        role: userData.role
      }
    })
  }
}

// Utilidades para assertions
export const testUtils = {
  // Verificar que un email es válido
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  // Verificar que una fecha es reciente (últimos 5 segundos)
  isRecentDate: (date: Date): boolean => {
    const now = new Date()
    const diffInMs = now.getTime() - date.getTime()
    return diffInMs >= 0 && diffInMs <= 5000 // 5 segundos
  },

  // Limpiar todas las tablas
  cleanDatabase: async () => {
    const tablenames = await prisma.$queryRaw<Array<{ name: string }>>`
      SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_prisma_migrations';
    `
    
    for (const { name } of tablenames) {
      await prisma.$executeRawUnsafe(`DELETE FROM "${name}";`)
    }
  }
}
