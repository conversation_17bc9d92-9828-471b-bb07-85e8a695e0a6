import { describe, it, expect, beforeEach } from '@jest/globals'
import { UserRepository } from '../../../src/repositories/user'
import { prisma } from '../../../lib/prisma'
import { testUtils } from '../../utils/testHelpers'
import bcrypt from 'bcryptjs'

describe('UserRepository', () => {
  let repository: UserRepository

  beforeEach(async () => {
    await testUtils.cleanDatabase()
    repository = new UserRepository(prisma)
  })

  describe('create', () => {
    it('should create a user with all required fields', async () => {
      const userData = {
        email: '<EMAIL>',
        hashed_password: await bcrypt.hash('password123', 10),
        role: 'ENGINEER' as const
      }

      const user = await repository.create(userData)

      expect(user).toBeDefined()
      expect(user.id).toBeDefined()
      expect(user.email).toBe(userData.email)
      expect(user.hashed_password).toBe(userData.hashed_password)
      expect(user.role).toBe(userData.role)
      expect(user.created_at).toBeDefined()
      expect(user.updated_at).toBeDefined()
    })

    it('should throw error when creating user with duplicate email', async () => {
      const userData = {
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        role: 'ENGINEER' as const
      }

      await repository.create(userData)

      await expect(repository.create(userData)).rejects.toThrow()
    })
  })

  describe('findById', () => {
    it('should return user when found', async () => {
      const userData = {
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        role: 'ADMIN' as const
      }
      const createdUser = await repository.create(userData)

      const foundUser = await repository.findById(createdUser.id)

      expect(foundUser).toBeDefined()
      expect(foundUser?.id).toBe(createdUser.id)
      expect(foundUser?.email).toBe(userData.email)
    })

    it('should return null when user not found', async () => {
      const foundUser = await repository.findById(999)
      expect(foundUser).toBeNull()
    })
  })

  describe('findByEmail', () => {
    it('should return user when found by email', async () => {
      const userData = {
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        role: 'SOCIAL_ASSISTANT' as const
      }
      await repository.create(userData)

      const foundUser = await repository.findByEmail(userData.email)

      expect(foundUser).toBeDefined()
      expect(foundUser?.email).toBe(userData.email)
      expect(foundUser?.role).toBe(userData.role)
    })

    it('should return null when user not found by email', async () => {
      const foundUser = await repository.findByEmail('<EMAIL>')
      expect(foundUser).toBeNull()
    })
  })

  describe('existsByEmail', () => {
    it('should return true when email exists', async () => {
      const userData = {
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        role: 'ENGINEER' as const
      }
      await repository.create(userData)

      const exists = await repository.existsByEmail(userData.email)
      expect(exists).toBe(true)
    })

    it('should return false when email does not exist', async () => {
      const exists = await repository.existsByEmail('<EMAIL>')
      expect(exists).toBe(false)
    })
  })

  describe('update', () => {
    it('should update user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        role: 'ENGINEER' as const
      }
      const createdUser = await repository.create(userData)

      const updateData = {
        email: '<EMAIL>',
        role: 'ADMIN' as const
      }

      const updatedUser = await repository.update(createdUser.id, updateData)

      expect(updatedUser.email).toBe(updateData.email)
      expect(updatedUser.role).toBe(updateData.role)
      expect(updatedUser.updated_at.getTime()).toBeGreaterThan(createdUser.updated_at.getTime())
    })
  })

  describe('delete', () => {
    it('should delete user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        role: 'ENGINEER' as const
      }
      const createdUser = await repository.create(userData)

      await repository.delete(createdUser.id)

      const foundUser = await repository.findById(createdUser.id)
      expect(foundUser).toBeNull()
    })
  })

  describe('findAll', () => {
    it('should return all users', async () => {
      const users = [
        { email: '<EMAIL>', hashed_password: 'hash1', role: 'ADMIN' as const },
        { email: '<EMAIL>', hashed_password: 'hash2', role: 'ENGINEER' as const }
      ]

      for (const userData of users) {
        await repository.create(userData)
      }

      const allUsers = await repository.findAll()
      expect(allUsers).toHaveLength(2)
    })

    it('should filter users by role', async () => {
      const users = [
        { email: '<EMAIL>', hashed_password: 'hash1', role: 'ADMIN' as const },
        { email: '<EMAIL>', hashed_password: 'hash2', role: 'ENGINEER' as const }
      ]

      for (const userData of users) {
        await repository.create(userData)
      }

      const adminUsers = await repository.findAll({ role: 'ADMIN' })
      expect(adminUsers).toHaveLength(1)
      expect(adminUsers[0].role).toBe('ADMIN')
    })
  })
})
