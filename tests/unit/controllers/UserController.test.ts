import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { UserController } from '../../../src/controllers/UserController'
import { CreateUserUseCase } from '../../../src/use-cases/CreateUserUseCase'
import { NextRequest } from 'next/server'

// Mock del CreateUserUseCase
const mockCreateUserUseCase: jest.Mocked<CreateUserUseCase> = {
  execute: jest.fn()
}

// Helper para crear mock de NextRequest
function createMockRequest(body: any): NextRequest {
  return {
    json: jest.fn().mockResolvedValue(body),
    method: 'POST',
    url: 'http://localhost:3000/api/users'
  } as any
}

describe('UserController', () => {
  let userController: UserController

  beforeEach(() => {
    jest.clearAllMocks()
    userController = new UserController(mockCreateUserUseCase)
  })

  describe('create', () => {
    const validRequestBody = {
      email: '<EMAIL>',
      password: 'password123',
      role: 'ENGINEER'
    }

    it('should create user successfully', async () => {
      const now = new Date()
      const expectedResponse = {
        user: {
          id: 1,
          email: '<EMAIL>',
          role: 'ENGINEER',
          created_at: now,
          updated_at: now
        }
      }

      mockCreateUserUseCase.execute.mockResolvedValue(expectedResponse)
      const mockRequest = createMockRequest(validRequestBody)

      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      expect(mockCreateUserUseCase.execute).toHaveBeenCalledWith(validRequestBody)
      expect(response.status).toBe(201)
      expect(responseData).toMatchObject({
        user: {
          id: 1,
          email: '<EMAIL>',
          role: 'ENGINEER'
        }
      })
      expect(responseData.user).toHaveProperty('created_at')
      expect(responseData.user).toHaveProperty('updated_at')
    })

    it('should handle validation errors with 400 status', async () => {
      const validationError = new Error('Email is required')
      mockCreateUserUseCase.execute.mockRejectedValue(validationError)

      const mockRequest = createMockRequest({ email: '', password: 'test', role: 'ENGINEER' })

      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({
        error: 'Email is required'
      })
    })

    it('should handle business logic errors with 400 status', async () => {
      const businessError = new Error('Email already exists')
      mockCreateUserUseCase.execute.mockRejectedValue(businessError)

      const mockRequest = createMockRequest(validRequestBody)

      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({
        error: 'Email already exists'
      })
    })

    it('should handle JSON parsing errors', async () => {
      const mockRequest = {
        json: jest.fn().mockRejectedValue(new Error('Invalid JSON'))
      } as any

      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData).toEqual({
        error: 'Invalid JSON'
      })
    })

    it('should handle unexpected errors with 500 status', async () => {
      const unexpectedError = new Error('Database connection failed')
      mockCreateUserUseCase.execute.mockRejectedValue(unexpectedError)

      const mockRequest = createMockRequest(validRequestBody)

      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      expect(response.status).toBe(500)
      expect(responseData).toEqual({
        error: 'Internal server error'
      })
    })

    it('should validate request body structure', async () => {
      const invalidRequestBody = {
        email: '<EMAIL>'
        // missing password and role
      }

      const mockRequest = createMockRequest(invalidRequestBody)

      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData.error).toContain('required')
    })

    it('should handle empty request body', async () => {
      const mockRequest = createMockRequest({})

      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData.error).toContain('required')
    })

    it('should handle null request body', async () => {
      const mockRequest = createMockRequest(null)

      const response = await userController.create(mockRequest)
      const responseData = await response.json()

      expect(response.status).toBe(400)
      expect(responseData.error).toContain('required')
    })
  })
})
