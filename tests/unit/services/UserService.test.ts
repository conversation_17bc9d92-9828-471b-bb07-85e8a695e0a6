import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { UserService } from '../../../src/services/UserService'
import { UserRepositoryInterface, User } from '../../../src/repositories/user'
import { CreateUserData } from '../../../src/types/entities/User'
import bcrypt from 'bcryptjs'

// Mock del repository
const mockUserRepository: jest.Mocked<UserRepositoryInterface> = {
  create: jest.fn(),
  findById: jest.fn(),
  findByEmail: jest.fn(),
  findAll: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  existsByEmail: jest.fn(),
  findByRole: jest.fn(),
  count: jest.fn()
}

describe('UserService', () => {
  let userService: UserService

  beforeEach(() => {
    jest.clearAllMocks()
    userService = new UserService(mockUserRepository)
  })

  describe('createUser', () => {
    const validUserData: CreateUserData = {
      email: '<EMAIL>',
      password: 'password123',
      role: 'ENGINEER'
    }

    it('should create user with hashed password', async () => {
      const expectedUser: User = {
        id: 1,
        email: validUserData.email,
        hashed_password: 'hashedpassword',
        role: validUserData.role,
        created_at: new Date(),
        updated_at: new Date()
      }

      mockUserRepository.existsByEmail.mockResolvedValue(false)
      mockUserRepository.create.mockResolvedValue(expectedUser)

      const result = await userService.createUser(validUserData)

      expect(mockUserRepository.existsByEmail).toHaveBeenCalledWith(validUserData.email)
      expect(mockUserRepository.create).toHaveBeenCalledWith({
        email: validUserData.email,
        hashed_password: expect.any(String),
        role: validUserData.role
      })
      expect(result).toEqual(expectedUser)
    })

    it('should hash password before storing', async () => {
      const expectedUser: User = {
        id: 1,
        email: validUserData.email,
        hashed_password: 'hashedpassword',
        role: validUserData.role,
        created_at: new Date(),
        updated_at: new Date()
      }

      mockUserRepository.existsByEmail.mockResolvedValue(false)
      mockUserRepository.create.mockResolvedValue(expectedUser)

      await userService.createUser(validUserData)

      const createCall = mockUserRepository.create.mock.calls[0][0]
      expect(createCall.hashed_password).not.toBe(validUserData.password)
      expect(createCall.hashed_password).toMatch(/^\$2[aby]\$/)
    })

    it('should throw error if email already exists', async () => {
      mockUserRepository.existsByEmail.mockResolvedValue(true)

      await expect(userService.createUser(validUserData)).rejects.toThrow('Email already exists')
      expect(mockUserRepository.create).not.toHaveBeenCalled()
    })

    it('should validate password strength', async () => {
      const weakPasswordData = {
        ...validUserData,
        password: '123'
      }

      await expect(userService.createUser(weakPasswordData)).rejects.toThrow('Password must be at least 6 characters long')
      expect(mockUserRepository.existsByEmail).not.toHaveBeenCalled()
    })

    it('should validate email format', async () => {
      const invalidEmailData = {
        ...validUserData,
        email: 'invalid-email'
      }

      await expect(userService.createUser(invalidEmailData)).rejects.toThrow('Invalid email format')
      expect(mockUserRepository.existsByEmail).not.toHaveBeenCalled()
    })

    it('should validate role', async () => {
      const invalidRoleData = {
        ...validUserData,
        role: 'INVALID_ROLE' as any
      }

      await expect(userService.createUser(invalidRoleData)).rejects.toThrow('Invalid role')
      expect(mockUserRepository.existsByEmail).not.toHaveBeenCalled()
    })
  })

  describe('getUserById', () => {
    it('should return user when found', async () => {
      const expectedUser: User = {
        id: 1,
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        role: 'ENGINEER',
        created_at: new Date(),
        updated_at: new Date()
      }

      mockUserRepository.findById.mockResolvedValue(expectedUser)

      const result = await userService.getUserById(1)

      expect(mockUserRepository.findById).toHaveBeenCalledWith(1)
      expect(result).toEqual(expectedUser)
    })

    it('should throw error when user not found', async () => {
      mockUserRepository.findById.mockResolvedValue(null)

      await expect(userService.getUserById(999)).rejects.toThrow('User not found')
    })
  })

  describe('getUserByEmail', () => {
    it('should return user when found', async () => {
      const expectedUser: User = {
        id: 1,
        email: '<EMAIL>',
        hashed_password: 'hashedpassword',
        role: 'ENGINEER',
        created_at: new Date(),
        updated_at: new Date()
      }

      mockUserRepository.findByEmail.mockResolvedValue(expectedUser)

      const result = await userService.getUserByEmail('<EMAIL>')

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith('<EMAIL>')
      expect(result).toEqual(expectedUser)
    })

    it('should return null when user not found', async () => {
      mockUserRepository.findByEmail.mockResolvedValue(null)

      const result = await userService.getUserByEmail('<EMAIL>')

      expect(result).toBeNull()
    })
  })

  describe('validatePassword', () => {
    it('should return true for valid password', async () => {
      const plainPassword = 'password123'
      const hashedPassword = await bcrypt.hash(plainPassword, 10)

      const user: User = {
        id: 1,
        email: '<EMAIL>',
        hashed_password: hashedPassword,
        role: 'ENGINEER',
        created_at: new Date(),
        updated_at: new Date()
      }

      const result = await userService.validatePassword(plainPassword, user)

      expect(result).toBe(true)
    })

    it('should return false for invalid password', async () => {
      const plainPassword = 'password123'
      const wrongPassword = 'wrongpassword'
      const hashedPassword = await bcrypt.hash(plainPassword, 10)

      const user: User = {
        id: 1,
        email: '<EMAIL>',
        hashed_password: hashedPassword,
        role: 'ENGINEER',
        created_at: new Date(),
        updated_at: new Date()
      }

      const result = await userService.validatePassword(wrongPassword, user)

      expect(result).toBe(false)
    })
  })

  describe('getAllUsers', () => {
    it('should return all users', async () => {
      const expectedUsers: User[] = [
        {
          id: 1,
          email: '<EMAIL>',
          hashed_password: 'hash1',
          role: 'ADMIN',
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          id: 2,
          email: '<EMAIL>',
          hashed_password: 'hash2',
          role: 'ENGINEER',
          created_at: new Date(),
          updated_at: new Date()
        }
      ]

      mockUserRepository.findAll.mockResolvedValue(expectedUsers)

      const result = await userService.getAllUsers()

      expect(mockUserRepository.findAll).toHaveBeenCalledWith(undefined)
      expect(result).toEqual(expectedUsers)
    })

    it('should return users filtered by role', async () => {
      const adminUsers: User[] = [
        {
          id: 1,
          email: '<EMAIL>',
          hashed_password: 'hash1',
          role: 'ADMIN',
          created_at: new Date(),
          updated_at: new Date()
        }
      ]

      mockUserRepository.findAll.mockResolvedValue(adminUsers)

      const result = await userService.getAllUsers({ role: 'ADMIN' })

      expect(mockUserRepository.findAll).toHaveBeenCalledWith({ role: 'ADMIN' })
      expect(result).toEqual(adminUsers)
    })
  })
})
