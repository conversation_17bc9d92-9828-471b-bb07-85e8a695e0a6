{"name": "actas-vecindad", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "db:seed": "tsx scripts/seed.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration"}, "dependencies": {"@prisma/client": "^6.8.2", "bcryptjs": "^3.0.2", "next": "15.1.8", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@jest/globals": "^29.7.0", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "postcss": "^8", "prisma": "^6.8.2", "tailwindcss": "^3.4.1", "ts-jest": "^29.3.4", "typescript": "^5"}}